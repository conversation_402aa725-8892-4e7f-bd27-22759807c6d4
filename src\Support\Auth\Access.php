<?php

namespace Support\Auth;

enum Access: string
{
    case ALL = 'all';

    /* ========== TEAM MEMBERS PERMISSIONS START ========== */
    /* USERS */
    case USERS_ACCESS = 'users_access';
    case USERS_VIEW = 'users_view';
    case USERS_CREATE = 'users_create';
    case USERS_EDIT = 'users_edit';
    case USERS_DELETE = 'users_delete';

    /* USER GROUPS */
    case USERGROUP_ACCESS = 'user_groups_access';
    case USERGROUP_VIEW = 'user_groups_view';
    case USERGROUP_CREATE = 'user_groups_create';
    case USERGROUP_EDIT = 'user_groups_edit';
    case USERGROUP_DELETE = 'user_groups_delete';

    /* STUDENT Profile Page Permissions */
    case STUDENT_PROFILE_ACCESS = 'student_profile_access';
    case STUDENT_PROFILE_VIEW = 'student_profile_view';
    case STUDENT_PROFILE_EDIT = 'student_profile_edit';

    case ENROLLMENTS_ACCESS = 'enrollments_access';
    case ENROLLMENTS_VIEW = 'enrollments_view';
    case ENROLLMENTS_CREATE = 'enrollments_create';
    case ENROLLMENTS_EDIT = 'enrollments_edit';
    case ENROLLMENTS_DELETE = 'enrollments_delete';

    case TIMETABLES_ACCESS = 'timetables_access';
    case TIMETABLES_VIEW = 'timetables_view';

    case ATTENDANCE_ACCESS = 'attendance_access';
    case ATTENDANCE_VIEW = 'attendance_view';

    case ASSESSMENTS_ACCESS = 'assessments_access';
    case ASSESSMENTS_VIEW = 'assessments_view';

    case PAYMENTS_ACCESS = 'payments_access';
    case PAYMENTS_VIEW = 'payments_view';
    case PAYMENTS_CREATE = 'payments_create';

    case SETTINGS_ACCESS = 'settings_access';
    case SETTINGS_VIEW = 'settings_view';
    case SETTINGS_EDIT = 'settings_edit';

    case ROLES_MANAGE = 'roles_manage';
    case ROLES_VIEW = 'roles_view';
    case ROLES_EDIT = 'roles_edit';
    case ROLES_DELETE = 'roles_delete';
    case ROLES_ASSIGN = 'roles_assign';
    case ROLES_ADD = 'roles_add';

    /* ========== ADMIN PERMISSIONS START ========== */

    /* DASHBOARD */
    case DASHBOARD_ACCESS = 'dashboard_access';

    /* TASK MANAGEMENT */
    case TASK_MANAGEMENT_ACCESS = 'task_management_access';
    case TASKS_ACCESS = 'tasks_access';
    case TASK_TEMPLATE_ACCESS = 'task_template_access';

    /* TASK MANAGEMENT BETA */
    case TASK_MANAGEMENT_BETA_ACCESS = 'task_management_beta_access';

    /* DOCUMENT */
    case DOCUMENT_ACCESS = 'document_access';

    /* APPLICATION */
    case APPLICATION_ACCESS = 'application_access';
    case NEW_ONLINE_APPLICATION_ACCESS = 'new_online_application_access';
    case CONTINUE_ONLINE_APPLICATION_ACCESS = 'continue_online_application_access';
    case MANAGE_OFFERS_ACCESS = 'manage_offers_access';
    case MANAGE_OFFERS_BETA_ACCESS = 'manage_offers_beta_access';
    case APPLY_SHORT_COURSE_ACCESS = 'apply_short_course_access';
    case OFFER_MAILING_LIST_ACCESS = 'offer_mailing_list_access';

    /* STUDENTS */
    case STUDENTS_ACCESS = 'students_access';

    /* ORIENTATION */
    case ORIENTATION_ACCESS = 'orientation_access';

    /* COHORTS */
    case COHORTS_ACCESS = 'cohorts_access';
    case SETUP_INTAKE_GROUP_ACCESS = 'setup_intake_group_access';
    case ASSIGN_STUDENT_GROUP_ACCESS = 'assign_student_group_access';
    case BULK_ENROLLMENT_GROUP_ACCESS = 'bulk_enrollment_group_access';
    case FLEXIBLE_TIMETABLE_ALLOCATION_ACCESS = 'flexible_timetable_allocation_access';
    case BULK_ENROLLMENT_SUBJECT_ACCESS = 'bulk_enrollment_subject_access';

    /* TIMETABLE */
    case TIMETABLE_ACCESS = 'timetable_access';
    case CLASS_TIMETABLE_ACCESS = 'class_timetable_access';
    case ASSESSMENT_GROUP_ACCESS = 'assessment_group_access';
    case TIMETABLE_REPORT_ACCESS = 'timetable_report_access';
    case VIEW_TIMETABLE_ACCESS = 'view_timetable_access';
    case REPLACEMENT_TEACHER_ACCESS = 'replacement_teacher_access';
    case PRINT_ATTENDANCE_LIST_ACCESS = 'print_attendance_list_access';

    /* TIMETABLE BETA */
    case TIMETABLE_BETA_ACCESS = 'timetable_beta_access';

    /* ========== ADMIN PERMISSIONS CONTINUED ========== */

    /* ATTENDANCE */
    // case ATTENDANCE_ACCESS = 'attendance_access';
    case ATTENDANCE_SUMMARY_ACCESS = 'attendance_summary_access';
    case ADD_CLASS_ATTENDANCE_ACCESS = 'add_class_attendance_access';
    case BULK_ATTENDANCE_ACCESS = 'bulk_attendance_access';
    case ATTENDANCE_REPORTS_ACCESS = 'attendance_reports_access';
    case WARNING_DAYS_SETTING_ACCESS = 'warning_days_setting_access';
    case WARNING_PERCENTAGE_SETTING_ACCESS = 'warning_percentage_setting_access';

    /* COMPETENCY */
    case COMPETENCY_ACCESS = 'competency_access';
    case SETUP_ASSESSMENT_TASK_ACCESS = 'setup_assessment_task_access';
    case TASK_ENTRY_ACCESS = 'task_entry_access';
    case TASK_RESULTS_ENTRY_ACCESS = 'task_results_entry_access';
    case TRANSFER_RESULTS_ACCESS = 'transfer_results_access';
    case VOCATIONAL_PLACEMENT_RESULT_ACCESS = 'vocational_placement_result_access';
    case TRANSFER_RESULTS_UNIT_ACCESS = 'transfer_results_unit_access';

    /* CERTIFICATE */
    case CERTIFICATE_ACCESS = 'certificate_access';
    case CERTIFICATE_TEMPLATES_ACCESS = 'certificate_templates_access';
    case GENERATE_BULK_CERTIFICATES_ACCESS = 'generate_bulk_certificates_access';

    /* INTERVENTION */
    case INTERVENTION_ACCESS = 'intervention_access';

    /* PAYMENT */
    case PAYMENT_ACCESS = 'payment_access';
    case PAYMENT_MAIN_ACCESS = 'payment_main_access';
    case INVOICE_ACCESS = 'invoice_access';
    case XERO_FAILED_TRANSACTION_ACCESS = 'xero_failed_transaction_access';

    /* INTEGRATIONS */
    case XERO_SETUP_ACCESS = 'xero_setup_access';
    case ZOHO_SETUP_ACCESS = 'zoho_setup_access';
    case MOODLE_SETUP_ACCESS = 'moodle_setup_access';
    case SSO_SETUP_ACCESS = 'sso_setup_access';
    case STRIPE_SETUP_ACCESS = 'stripe_setup_access';
    case SHORTCOURSE_SETUP_ACCESS = 'shortcourse_setup_access';
    case WEBHOOK_SETUP_ACCESS = 'webhook_setup_access';

    /* USI */
    case USI_ACCESS = 'usi_access';

    /* COMMUNICATIONS */
    case COMMUNICATIONS_ACCESS = 'communications_access';

    /* CONTINUOUS IMPROVEMENT */
    case CONTINUOUS_IMPROVEMENT_ACCESS = 'continuous_improvement_access';

    /* PLACEMENT */
    case PLACEMENT_ACCESS = 'placement_access';

    /* RISK ASSESSMENT MATRIX */
    case RISK_ASSESSMENT_MATRIX_ACCESS = 'risk_assessment_matrix_access';

    /* TRAINERS LIST */
    case TRAINERS_LIST_ACCESS = 'trainers_list_access';

    /* ASSIGN UNITS */
    case ASSIGN_UNITS_ACCESS = 'assign_units_access';

    /* TIMESHEET */
    case TIMESHEET_ACCESS = 'timesheet_access';
    case PAY_PERIOD_ACCESS = 'pay_period_access';
    case PAY_PERIOD_BETA_ACCESS = 'pay_period_beta_access';
    case MANAGE_TIMESHEET_ACCESS = 'manage_timesheet_access';
    case MANAGE_TIMESHEET_BETA_ACCESS = 'manage_timesheet_beta_access';
    case SUBMIT_TIMESHEET_ACCESS = 'submit_timesheet_access';
    case SUBMIT_TIMESHEET_BETA_ACCESS = 'submit_timesheet_beta_access';
    case APPROVED_TIMESHEET_ACCESS = 'approved_timesheet_access';
    case APPROVED_TIMESHEET_BETA_ACCESS = 'approved_timesheet_beta_access';
    case SUPERVISOR_ACCESS = 'supervisor_access';
    case SUPERVISOR_BETA_ACCESS = 'supervisor_beta_access';

    /* TRAINEESHIP */
    case TRAINEESHIP_APPRENTICESHIP_ACCESS = 'traineeship_apprenticeship_access';
    case TRAINEESHIP_APPRENTICESHIP_BETA_ACCESS = 'traineeship_apprenticeship_beta_access';

    /* SETUP SERVICES */
    case SETUP_SERVICES_ACCESS = 'setup_services_access';
    case SERVICE_PROVIDERS_ACCESS = 'service_providers_access';
    case ASSIGN_FACILITY_ACCESS = 'assign_facility_access';
    case UPDATE_SERVICE_ALLOCATION_ACCESS = 'update_service_allocation_access';
    case PROVIDER_PAYMENT_ACCESS = 'provider_payment_access';

    /* AGENTS */
    case AGENT_LIST_ACCESS = 'agent_list_access';
    case ADD_AGENT_ACCESS = 'add_agent_access';
    case AGENT_MAILING_LIST_ACCESS = 'agent_mailing_list_access';
    case AGENT_PAYMENT_ACCESS = 'agent_payment_access';
    case AGENT_COMMISSION_ACCESS = 'agent_commission_access';
    case AGENT_COMMUNICATION_LOG_ACCESS = 'agent_communication_log_access';

    /* EMPLOYERS */
    case EMPLOYER_LIST_ACCESS = 'employer_list_access';
    case EMPLOYER_BETA_ACCESS = 'employer_beta_access';
    case EMPLOYER_INVOICE_ACCESS = 'employer_invoice_access';

    /* STAFF */
    case STAFF_LIST_ACCESS = 'staff_list_access';
    case SETUP_PERMISSION_STAFF_ACCESS = 'setup_permission_staff_access';
    case EVALUATION_ACCESS = 'evaluation_access';
    case LEAVE_INFO_ACCESS = 'leave_info_access';

    /* STAFF PAYMENT */
    case STAFF_PAYMENT_ACCESS = 'staff_payment_access';
    case PROCESS_TIMESHEET_ACCESS = 'process_timesheet_access';
    case VIEW_TIMESHEET_INVOICE_ACCESS = 'view_timesheet_invoice_access';

    /* USER MANAGEMENT */
    case MANAGE_USER_ACCOUNT_ACCESS = 'manage_user_account_access';
    case STUDENTS_USER_ACCOUNT_MANAGEMENT_ACCESS = 'students_user_account_management_access';
    case CREATE_STUDENTS_USER_ACCOUNT_ACCESS = 'create_students_user_account_access';
    case IP_TRACK_ACCESS = 'ip_track_access';

    /* COURSES */
    case COURSES_ACCESS = 'courses_access';
    case MANAGE_COURSES_ACCESS = 'manage_courses_access';
    case TRAINING_PLAN_TEMPLATE_ACCESS = 'training_plan_template_access';

    /* UPDATE STATUS */
    case UPDATE_STATUS_ACCESS = 'update_status_access';
    case UPDATE_STUDENT_COURSE_ACCESS = 'update_student_course_access';
    case UPDATE_STUDENT_COURSE_BETA_ACCESS = 'update_student_course_beta_access';
    case UPDATE_STUDENT_COURSE_TEMPLATE_ACCESS = 'update_student_course_template_access';
    case UPDATE_STUDENT_COURSE_TEMPLATE_BETA_ACCESS = 'update_student_course_template_beta_access';

    /* DATA REPORTING */
    case DATA_REPORTING_ACCESS = 'data_reporting_access';
    case VALIDATE_PRISMS_ACCESS = 'validate_prisms_access';
    case VALIDATE_PRISMS_BETA_ACCESS = 'validate_prisms_beta_access';
    case AVETMISS_ACCESS = 'avetmiss_access';
    case AVETMISS_BETA_ACCESS = 'avetmiss_beta_access';
    case NVR_ACCESS = 'nvr_access';
    case NVR_BETA_ACCESS = 'nvr_beta_access';
    case CQR_ACCESS = 'cqr_access';
    case CQR_BETA_ACCESS = 'cqr_beta_access';
    case VET_FEE_HELP_ACCESS = 'vet_fee_help_access';

    /* SURVEY MANAGEMENT */
    case SURVEY_MANAGEMENT_ACCESS = 'survey_management_access';
    case SURVEY_MANAGEMENT_BETA_ACCESS = 'survey_management_beta_access';
    case SURVEY_ACTIVATION_ACCESS = 'survey_activation_access';
    case SURVEY_ACTIVATION_BETA_ACCESS = 'survey_activation_beta_access';
    case SURVEY_RESULTS_NOT_SENT_ACCESS = 'survey_results_not_sent_access';
    case SURVEY_RESULTS_NOT_SENT_BETA_ACCESS = 'survey_results_not_sent_beta_access';
    case SURVEY_RESULTS_SENT_ACCESS = 'survey_results_sent_access';
    case SURVEY_RESULTS_SENT_BETA_ACCESS = 'survey_results_sent_beta_access';

    /* LETTER TEMPLATE */
    case LETTER_TEMPLATE_ACCESS = 'letter_template_access';
    case PDF_TEMPLATE_LIST_ACCESS = 'pdf_template_list_access';
    case PDF_TEMPLATE_LIST_BETA_ACCESS = 'pdf_template_list_beta_access';

    /* MANAGE CALENDAR */
    case MANAGE_CALENDAR_ACCESS = 'manage_calendar_access';
    case MANAGE_SEMESTER_ACCESS = 'manage_semester_access';
    case MANAGE_SEMESTER_BETA_ACCESS = 'manage_semester_beta_access';
    case SEMESTER_DIVISION_ACCESS = 'semester_division_access';
    case COURSE_CALENDAR_TYPE_ACCESS = 'course_calendar_type_access';

    /* MANAGE TRAINING CONTRACTS */
    case MANAGE_TRAINING_CONTRACTS_ACCESS = 'manage_training_contracts_access';
    case ADD_CONTRACT_ACCESS = 'add_contract_access';
    case ADD_CONTRACT_BETA_ACCESS = 'add_contract_beta_access';

    /* PROFILE */
    case EDIT_MY_PROFILE_ACCESS = 'edit_my_profile_access';

    /* ORGANISATION PROFILE */
    case ORGANISATION_PROFILE_ACCESS = 'organisation_profile_access';
    case GENERAL_INFO_ACCESS = 'general_info_access';
    case TRAINING_ORGANISATION_IDENTIFIER_ACCESS = 'training_organisation_identifier_access';
    case OFFER_MAIN_LOCATION_ACCESS = 'offer_main_location_access';
    case BANK_DETAILS_ACCESS = 'bank_details_access';
    case VSL_INFO_ACCESS = 'vsl_info_access';

    /* COURSES SECOND GROUP */
    case COURSES_SECOND_ACCESS = 'courses_second_access';
    case CAMPUS_VENUES_ROOMS_ACCESS = 'campus_venues_rooms_access';
    case COURSE_TYPE_ACCESS = 'course_type_access';
    case MANAGE_COURSES_SPA_ACCESS = 'manage_courses_spa_access';
    case ELICOS_DISCOUNT_WEEK_ACCESS = 'elicos_discount_week_access';
    case INTAKE_DATES_ACCESS = 'intake_dates_access';
    case COURSE_TEMPLATE_ACCESS = 'course_template_access';
    case COURSE_UPFRONT_FEE_ACCESS = 'course_upfront_fee_access';
    case COURSE_PROMOTION_ACCESS = 'course_promotion_access';

    /* Cohorts Group */
    /*
    case COHORTS_ACCESS = 'cohorts_access';
    case SETUP_INTAKE_GROUP_ACCESS = 'setup_intake_group_access';
    case ADD_INTAKE_GROUP = 'add_intake_group';
    case DELETE_INTAKE_GROUP = 'delete_intake_group';
    case ASSIGN_STUDENT_TO_INTAKE_GROUP = 'assign_student_to_intake_group';
    case UNASSIGN_STUDENT_TO_INTAKE_GROUP = 'unassign_student_to_intake_group';
    case TIMETABLE_ALLOCATION_BY_GROUP_ACCESS = 'timetable_allocation_by_group_access';
    case BULK_ENROLMENT_BY_GROUP_ACCESS = 'bulk_enrolment_by_group_access';
    case BULK_ENROLMENT_BY_SUBJECT_ACCESS = 'bulk_enrolment_by_subject_access';
    */
    /* ORGANIZATION OPERATIONS */
    case ORGANIZATION_OPERATIONS_ACCESS = 'organization_operations_access';
    case INTERVENTION_STRATEGY_ACCESS = 'intervention_strategy_access';
    case SECTION_SETUP_ACCESS = 'section_setup_access';
    case UPDATE_FINAL_OUTCOME_ACCESS = 'update_final_outcome_access';
    case UPDATE_STUDENT_COURSE_STATUS_ACCESS = 'update_student_course_status_access';
    case PUBLIC_COLLEGE_HOLIDAYS_ACCESS = 'public_college_holidays_access';
    case FORMS_ACCESS = 'forms_access';

    /* TRAINING */
    case TRAINING_ACCESS = 'training_access';
    case MANAGE_CONTRACT_ACCESS = 'manage_contract_access';
    case MANAGE_CONTRACT_SCHEDULE_ACCESS = 'manage_contract_schedule_access';
    case VIEW_COURSE_SITE_ACCESS = 'view_course_site_access';

    /* STUDENTS SECOND GROUP */
    case STUDENTS_SERVICES_ACCESS = 'students_services_access';
    case OVERSEAS_STUDENT_HEALTH_COVERAGE_ACCESS = 'overseas_student_health_coverage_access';
    case STUDENT_SERVICES_INFORMATION_ACCESS = 'student_services_information_access';
    case STUDENT_ID_FORMAT_ACCESS = 'student_id_format_access';

    /* AGENTS */
    case AGENTS_ACCESS = 'agents_access';
    case AGENT_DOCUMENT_CHECKLIST_ACCESS = 'agent_document_checklist_access';
    case AGENT_STATUS_ACCESS = 'agent_status_access';

    /* APPLICATIONS */
    case APPLICATIONS_ACCESS = 'applications_access';
    case COUNTRY_LIST_ACCESS = 'country_list_access';
    case LANGUAGE_LIST_ACCESS = 'language_list_access';

    /* TEMPLATES/LETTERS */
    case TEMPLATES_LETTERS_ACCESS = 'templates_letters_access';
    case ADD_EDIT_LETTER_ACCESS = 'add_edit_letter_access';
    case ADD_EDIT_EMAIL_TEMPLATE_ACCESS = 'add_edit_email_template_access';
    case ADD_EDIT_SMS_TEMPLATE_ACCESS = 'add_edit_sms_template_access';
    case CHECKLIST_ACCESS = 'checklist_access';
    case LETTER_SETTING_ACCESS = 'letter_setting_access';
    case FAILED_EMAILS_ACCESS = 'failed_emails_access';

    /* FINANCE */
    case FINANCE_ACCESS = 'finance_access';
    case ENROLLMENT_FEE_ACCESS = 'enrollment_fee_access';
    case RECONCILE_BANK_ACCESS = 'reconcile_bank_access';
    case RECONCILE_BANK_BETA_ACCESS = 'reconcile_bank_beta_access';
    case SETUP_ACCOUNT_PAYMENT_ACCESS = 'setup_account_payment_access';
    case SETUP_ACCOUNT_PAYMENT_BETA_ACCESS = 'setup_account_payment_beta_access';
    case MANAGE_BANK_INFORMATION_ACCESS = 'manage_bank_information_access';
    case INVOICE_SETTINGS_ACCESS = 'invoice_settings_access';
    case BILLING_ACCESS = 'billing_access';

    /* DATA REPORTING SECOND GROUP */
    case DATA_REPORTING_SECOND_ACCESS = 'data_reporting_second_access';
    case REPORTS_ACCESS = 'reports_access';

    /* OTHER */
    case OTHER_ACCESS = 'other_access';
    case COMPETENCY_GRADE_ACCESS = 'competency_grade_access';
    case SMTP_SETUP_ACCESS = 'smtp_setup_access';
    case FAILED_JOBS_ACCESS = 'failed_jobs_access';
    case PLACEMENT_PROVIDER_ACCESS = 'placement_provider_access';
    case CERTIFICATE_ID_FORMAT_ACCESS = 'certificate_id_format_access';
    case EXTEND_ASSESSMENT_DUE_DATE_ACCESS = 'extend_assessment_due_date_access';
    case NOTIFICATION_SETTING_ACCESS = 'notification_setting_access';
    case OFFER_LABEL_ACCESS = 'offer_label_access';
    case ASQA_AUDIT_REPORT_ACCESS = 'asqa_audit_report_access';
    case GLOBAL_QUEUE_ACCESS = 'global_queue_access';

    /* ONBOARD SETUP */
    case ONBOARD_SETUP_ACCESS = 'onboard_setup_access';

    /* ========== STAFF PERMISSIONS END ========== */

    /* ========== TEACHER PERMISSIONS START ========== */

    /* DASHBOARD */
    case TP_DASHBOARD_ACCESS = 'tp_dashboard_access';

    /* TASKS MANAGEMENT */
    case TP_TASKS_MANAGEMENT_ACCESS = 'tp_tasks_management_access';

    /* DOCUMENTS */
    case TP_DOCUMENTS_ACCESS = 'tp_documents_access';
    case TP_COLLEGE_DOCUMENTS_ACCESS = 'tp_college_documents_access';
    case TP_SUBJECT_SPECIFIC_ACCESS = 'tp_subject_specific_access';
    case TP_COURSE_SPECIFIC_ACCESS = 'tp_course_specific_access';

    /* VIEW TIMETABLE */
    case TP_VIEW_TIMETABLE_ACCESS = 'tp_view_timetable_access';

    /* TIMESHEET */
    case TP_TIMESHEET_ACCESS = 'tp_timesheet_access';
    case TP_TIMESHEET_SUBMISSION_ACCESS = 'tp_timesheet_submission_access';
    case TP_EXTRA_TIMESHEET_SUBMISSION_ACCESS = 'tp_extra_timesheet_submission_access';
    case TP_VIEW_TIMESHEET_ACCESS = 'tp_view_timesheet_access';
    case TP_UPLOAD_INVOICE_ACCESS = 'tp_upload_invoice_access';

    /* ATTENDANCE */
    case TP_ATTENDANCE_ACCESS = 'tp_attendance_access';
    case TP_DAILY_ATTENDANCE_ACCESS = 'tp_daily_attendance_access';

    /* COMPETENCY */
    case TP_COMPETENCY_ACCESS = 'tp_competency_access';
    case TP_RESULT_MANAGEMENT_ACCESS = 'tp_result_management_access';
    case TP_TRANSFER_RESULTS_ACCESS = 'tp_transfer_results_access';
    case TP_VOCATIONAL_PLACEMENT_RESULT_ACCESS = 'tp_vocational_placement_result_access';
    case TP_TRANSFER_RESULTS_BY_UNIT_ACCESS = 'tp_transfer_results_by_unit_access';

    /* TRAINEESHIP */
    case TP_TRAINEESHIP_ACCESS = 'tp_traineeship_access';
    case TP_TRAINEESHIP_ACTIVITY_ACCESS = 'tp_traineeship_activity_access';
    case TP_ACTIVITY_BETWEEN_DATES_ACCESS = 'tp_activity_between_dates_access';

    /* COMMUNICATION */
    case TP_COMMUNICATION_ACCESS = 'tp_communication_access';
    case TP_MAILING_LIST_ACCESS = 'tp_mailing_list_access';
    case TP_COMMUNICATION_LOG_ACCESS = 'tp_communication_log_access';
    case TP_REQUEST_TO_ADMIN_ACCESS = 'tp_request_to_admin_access';

    /* OTHERS */
    case TP_OTHERS_ACCESS = 'tp_others_access';
    case TP_REPORTS_ACCESS = 'tp_reports_access';
    case TP_ELEARNING_LINK_ACCESS = 'tp_elearning_link_access';
    case TP_CONTINUOUS_IMPROVEMENT_ACCESS = 'tp_continuous_improvement_access';
    case TP_ADD_TEACHER_REGISTER_IMPROVEMENT_ACCESS = 'tp_add_teacher_register_improvement_access';
    case TP_EDIT_TEACHER_REGISTER_IMPROVEMENT_ACCESS = 'edit_teacher_register_improvement_access';
    case TP_LEAVE_INFO_ACCESS = 'tp_leave_info_access';
    case TP_EVALUATION_ACCESS = 'tp_evaluation_access';
    case TP_EDIT_PROFILE_ACCESS = 'tp_edit_profile_access';

    /* ======== TEACHER PERMISSIONS END ========== */

    /* ========== TEAM MEMBERS PERMISSIONS END ========== */

    /* ========== AGENT PERMISSIONS START ========== */

    /* STUDENT */
    case AP_STUDENT_ACCESS = 'ap_student_access';
    case AP_STUDENT_PROFILE_ACCESS = 'ap_student_profile_access';
    case AP_STUDENT_PROFILE_VIEW = 'ap_student_profile_view';
    case AP_STUDENT_LIST_ACCESS = 'ap_student_list_access';
    case AP_STUDENT_APPLICATIONS_ACCESS = 'ap_student_applications_access';
    case AP_OFFERED_STUDENT_ACCESS = 'ap_offered_student_access';
    case AP_CONTINUE_APPLICATION_ACCESS = 'ap_continue_application_access';
    case AP_GTE_DASHBOARD_ACCESS = 'ap_gte_dashboard_access';
    case AP_OFFER_COMMUNICATION_ACCESS = 'ap_offer_communication_access';
    case AP_COMMUNICATION_ARCHIVES_ACCESS = 'ap_communication_archives_access';

    /* DOCUMENT */
    case AP_DOCUMENT_ACCESS = 'ap_document_access';

    /* ========== AGENT PERMISSIONS END ========== */

    /* ========== STUDENT PERMISSIONS START ========== */
    /* Student MODULES */

    /* DASHBOARD */
    case SP_DASHBOARD_ACCESS = 'sp_dashboard_access';

    /* DOCUMENT */
    case SP_DOCUMENT_ACCESS = 'sp_document_access';

    /* OSHC */
    case SP_OSHC_ACCESS = 'sp_oshc_access';

    /* ATTENDANCE */
    case SP_ATTENDANCE_ACCESS = 'sp_attendance_access';

    /* RESULTS */
    case SP_RESULTS_ACCESS = 'sp_results_access';

    /* TIMETABLE */
    case SP_TIMETABLE_ACCESS = 'sp_timetable_access';

    /* PAYMENT */
    case SP_PAYMENT_ACCESS = 'sp_payment_access';

    /* WARNING LOG */
    case SP_WARNING_LOG_ACCESS = 'sp_warning_log_access';

    /* COMMUNICATION */
    case SP_COMMUNICATION_ACCESS = 'sp_communication_access';
    case SP_EMAIL_FEEDBACK_ACCESS = 'sp_email_feedback_access';
    case SP_EMAIL_TO_TRAINER_ACCESS = 'sp_email_to_trainer_access';
    case SP_EMAIL_COMMUNICATION_LOG_ACCESS = 'sp_email_communication_log_access';

    /* FORMS */
    case SP_FORMS_ACCESS = 'sp_forms_access';

    /* EDIT PROFILE */
    case SP_EDIT_PROFILE_ACCESS = 'sp_edit_profile_access';

    /* ========== STUDENT PERMISSIONS END ========== */

    /* ========== SSP PERMISSIONS START ========== */

    /* DASHBOARD */
    case SPP_DASHBOARD_ACCESS = 'spp_dashboard_access';

    /* DOCUMENTS */
    case SPP_COLLEGE_DOCUMENTS_ACCESS = 'spp_college_documents_access';

    /* APPLICATION */
    case SPP_APPLICATION_ACCESS = 'spp_application_access';
    case SPP_NEW_ONLINE_APPLICATION_ACCESS = 'spp_new_online_application_access';
    case SPP_CONTINUE_ONLINE_APPLICATION_ACCESS = 'spp_continue_online_application_access';
    case SPP_MANAGE_OFFERS_ACCESS = 'spp_manage_offers_access';
    case SPP_MANAGE_OFFERS_BETA_ACCESS = 'spp_manage_offers_beta_access';
    case SPP_APPLY_SHORT_COURSE_ACCESS = 'spp_apply_short_course_access';
    case SPP_OFFER_MAILING_LIST_ACCESS = 'spp_offer_mailing_list_access';

    /* ORIENTATION */
    case SPP_ORIENTATION_ACCESS = 'spp_orientation_access';

    /* COHORTS */
    case SPP_COHORTS_ACCESS = 'spp_cohorts_access';
    case SPP_SETUP_INTAKE_GROUP_ACCESS = 'spp_setup_intake_group_access';
    case SPP_ASSIGN_STUDENT_TO_GROUP_ACCESS = 'spp_assign_student_to_group_access';
    case SPP_BULK_ENROLLMENT_BY_GROUP_ACCESS = 'spp_bulk_enrollment_by_group_access';
    case SPP_FLEXIBLE_TIMETABLE_ALLOCATION_ACCESS = 'spp_flexible_timetable_allocation_access';
    case SPP_BULK_ENROLLMENT_BY_SUBJECT_ACCESS = 'spp_bulk_enrollment_by_subject_access';

    /* ATTENDANCE */
    case SPP_ATTENDANCE_ACCESS = 'spp_attendance_access';
    case SPP_ATTENDANCE_SUMMARY_ACCESS = 'spp_attendance_summary_access';
    case SPP_ADD_CLASS_ATTENDANCE_ACCESS = 'spp_add_class_attendance_access';
    case SPP_BULK_ATTENDANCE_ACCESS = 'spp_bulk_attendance_access';
    case SPP_REPORTS_ACCESS = 'spp_reports_access';
    case SPP_WARNING_DAYS_SETTING_ACCESS = 'spp_warning_days_setting_access';
    case SPP_WARNING_PERCENTAGE_SETTING_ACCESS = 'spp_warning_percentage_setting_access';

    /* COMPETENCY */
    case SPP_COMPETENCY_ACCESS = 'spp_competency_access';
    case SPP_SETUP_ASSESSMENT_TASK_ACCESS = 'spp_setup_assessment_task_access';
    case SPP_TASK_ENTRY_ACCESS = 'spp_task_entry_access';
    case SPP_TASK_RESULTS_ENTRY_ACCESS = 'spp_task_results_entry_access';
    case SPP_TRANSFER_RESULTS_ACCESS = 'spp_transfer_results_access';
    case SPP_VOCATIONAL_PLACEMENT_RESULT_ACCESS = 'spp_vocational_placement_result_access';
    case SPP_TRANSFER_RESULTS_BY_UNIT_ACCESS = 'spp_transfer_results_by_unit_access';

    /* CERTIFICATE */
    case SPP_CERTIFICATE_ACCESS = 'spp_certificate_access';

    /* INTERVENTION */
    case SPP_INTERVENTION_ACCESS = 'spp_intervention_access';

    /* TRAINEESHIP */
    case SPP_TRAINEESHIP_ACCESS = 'spp_traineeship_access';

    /* SERVICE MANAGEMENT */
    case SPP_SERVICE_MANAGEMENT_ACCESS = 'spp_service_management_access';
    case SPP_SERVICE_FEE_SETUP_ACCESS = 'spp_service_fee_setup_access';
    case SPP_PROVIDER_SETUP_ACCESS = 'spp_provider_setup_access';
    case SPP_ALLOCATE_PROVIDER_ACCESS = 'spp_allocate_provider_access';

    /* BULK UPDATE */
    case SPP_BULK_UPDATE_ACCESS = 'spp_bulk_update_access';
    case SPP_UPDATE_FINAL_OUTCOME_ACCESS = 'spp_update_final_outcome_access';
    case SPP_UPDATE_STUDENT_COURSE_STATUS_ACCESS = 'spp_update_student_course_status_access';
    case SPP_UPDATE_STUDENT_COURSE_ACCESS = 'spp_update_student_course_access';
    case SPP_UPDATE_STUDENT_COURSE_TEMPLATE_ACCESS = 'spp_update_student_course_template_access';

    /* OTHERS */
    case SPP_OTHERS_ACCESS = 'spp_others_access';
    case SPP_PRISMS_DATA_VALIDATION_ACCESS = 'spp_prisms_data_validation_access';
    case SPP_GENERATE_REPORTS_ACCESS = 'spp_generate_reports_access';
    case SPP_COMMUNICATION_ACCESS = 'spp_communication_access';
    case SPP_CONTINUOUS_IMPROVEMENT_ACCESS = 'spp_continuous_improvement_access';
    case SPP_FORMS_ACCESS = 'spp_forms_access';
    case SPP_EDIT_PROFILE_ACCESS = 'spp_edit_profile_access';
    case SPP_LEAVE_INFO_ACCESS = 'spp_leave_info_access';

    /* ========== SSP PERMISSIONS END ========== */

    /**
     * Return [CASE_NAME => value] format.
     */
    public static function toArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[$case->name] = $case->value;
        }

        return $array;
    }
}
